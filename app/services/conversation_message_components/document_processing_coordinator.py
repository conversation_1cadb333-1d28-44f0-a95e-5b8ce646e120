"""
Document processing coordinator for handling document processing triggers.

This coordinator determines when document processing should be triggered and
coordinates the document processing workflow.
"""

import logging
from uuid import UUID

from fastapi import UploadFile

from constants.message import ConversationMessageIntention
from schemas import DocumentCreationRequest
from services.document import DocumentService


__all__ = ['DocumentProcessingCoordinator']

logger = logging.getLogger(__name__)


class DocumentProcessingCoordinator:
    """
    Coordinates document processing workflows.
    
    This class handles the logic for determining when document processing should
    be triggered and coordinates the document processing workflow.
    """

    def __init__(self, document_service: DocumentService):
        self.document_service = document_service

    def should_trigger_processing(
        self, 
        content: str, 
        intention: ConversationMessageIntention | None, 
        files: list[UploadFile] | None
    ) -> bool:
        """
        Determine if document processing should be triggered.
        
        Args:
            content: Text content of the message
            intention: The classified intention of the message
            files: Optional list of files attached to the message
            
        Returns:
            bool: True if document processing should be triggered
        """
        return bool(files or (content and intention == ConversationMessageIntention.EXTRACTION))

    async def trigger_document_processing(
        self,
        conversation_id: UUID,
        message_id: UUID,
        files: list[UploadFile] | None,
        content: str,
        intention: ConversationMessageIntention | None,
    ) -> list | None:
        """
        Trigger document processing for files and/or text content.
        
        Args:
            conversation_id: The conversation ID
            message_id: The message ID
            files: Optional list of files to process
            content: Text content to process
            intention: The classified intention of the message
            
        Returns:
            list | None: Document processing responses or None
        """
        if not self.should_trigger_processing(content, intention, files):
            return None

        logger.debug(
            'Triggering document processing for conversation %s, message %s', 
            conversation_id, message_id
        )

        document_data = DocumentCreationRequest(
            conversation_id=conversation_id,
            files=files or [],
            message_id=message_id,
        )

        # Use unified approach: send text content for extraction along with files
        text_for_extraction = content if intention == ConversationMessageIntention.EXTRACTION else None
        
        try:
            return await self.document_service.create_combined_message(document_data, text_for_extraction)
        except Exception as e:
            logger.error(
                'Error processing documents for conversation %s, message %s: %s', 
                conversation_id, message_id, e
            )
            raise
