"""
Option handlers for different types of user selections in conversation messages.

This module implements the Strategy pattern to handle different option types,
making it easy to add new option types without modifying existing code.
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict
from uuid import UUID

from constants.extracted_data import ConfirmedDataFields, ConversationState
from constants.message import MessageR<PERSON>, MessageType, OptionType, SystemReplyType
from schemas import (
    ClientNameOption,
    DatePickerOption,
    LDMFCountryOption,
    MessageValidator,
    Option,
)
from services.extracted_data import ExtractedDataService
from services.kx_dash import KXDashService


__all__ = ['OptionHandlerFactory', 'OptionHandler']

logger = logging.getLogger(__name__)


class OptionHandler(ABC):
    """Abstract base class for option handlers."""

    @abstractmethod
    async def handle(self, selected_option: Option, conversation_id: UUID, **kwargs) -> MessageValidator:
        """
        Handle the selected option and return a system message.

        Args:
            selected_option: The selected option
            conversation_id: The conversation ID
            **kwargs: Additional arguments specific to the handler

        Returns:
            MessageValidator: System message confirming the selection
        """
        pass


class ClientNameOptionHandler(OptionHandler):
    """Handler for client name option selections."""

    def __init__(self, extracted_data_service: ExtractedDataService):
        self.extracted_data_service = extracted_data_service

    async def handle(self, selected_option: Option, conversation_id: UUID, **kwargs) -> MessageValidator:
        """Handle client name selection from options."""
        if not isinstance(selected_option, ClientNameOption):
            raise ValueError(f"Expected ClientNameOption, got {type(selected_option)}")

        try:
            logger.debug(
                'Handling client name selection: %s for conversation: %s', 
                selected_option.client_name, conversation_id
            )

            # Update confirmed data with the selected client name
            await self.extracted_data_service.update_confirmed_data(
                conversation_id=conversation_id,
                field_name='client_name',
                field_value=selected_option.client_name,
                state=ConversationState.COLLECTING_COUNTRY,  # Move to next state
            )

            # Create confirmation message
            reply_type = SystemReplyType.CLIENT_NAME_CONFIRMED
            confirmation_message = reply_type.message_text.format(client_name=selected_option.client_name)

            return MessageValidator(
                conversation_id=conversation_id,
                role=MessageRole.SYSTEM,
                type=MessageType.TEXT,
                content=confirmation_message,
                selected_option=selected_option,
                system_reply_type=reply_type,
            )

        except Exception as e:
            logger.error('Error handling client name selection: %s', e)
            raise


class CountryOptionHandler(OptionHandler):
    """Handler for LDMF country option selections."""

    def __init__(self, extracted_data_service: ExtractedDataService):
        self.extracted_data_service = extracted_data_service

    async def handle(self, selected_option: Option, conversation_id: UUID, **kwargs) -> MessageValidator:
        """Handle country selection from options."""
        if not isinstance(selected_option, LDMFCountryOption):
            raise ValueError(f"Expected LDMFCountryOption, got {type(selected_option)}")

        try:
            logger.debug(f'Handling selection {selected_option.ldmf_country} for conversation {conversation_id}')

            # Update confirmed data with the selected country
            await self.extracted_data_service.update_confirmed_data(
                conversation_id=conversation_id,
                field_name='ldmf_country',
                field_value=selected_option.ldmf_country,
                state=ConversationState.COLLECTING_DATES,
            )

            # Create confirmation message
            reply_type = SystemReplyType.LDMF_COUNTRY_CONFIRMED
            confirmation_message = reply_type.message_text.format(ldmf_country=selected_option.ldmf_country)
            return MessageValidator(
                conversation_id=conversation_id,
                role=MessageRole.SYSTEM,
                type=MessageType.TEXT,
                content=confirmation_message,
                selected_option=selected_option,
                system_reply_type=reply_type,
            )

        except Exception as e:
            logger.error('Error handling country selection: %s', e)
            raise


class DatesOptionHandler(OptionHandler):
    """Handler for date picker option selections."""

    def __init__(self, extracted_data_service: ExtractedDataService):
        self.extracted_data_service = extracted_data_service

    async def handle(self, selected_option: Option, conversation_id: UUID, **kwargs) -> MessageValidator:
        """Handle dates selection from options."""
        if not isinstance(selected_option, DatePickerOption):
            raise ValueError(f"Expected DatePickerOption, got {type(selected_option)}")

        try:
            logger.debug('Handling dates selection: %s for conversation: %s', selected_option, conversation_id)
            start_date = selected_option.start_date.isoformat() if selected_option.start_date else None
            end_date = selected_option.end_date.isoformat() if selected_option.end_date else None

            # Create confirmation message
            reply_type = SystemReplyType.DATES_CONFIRMED
            confirmation_message = reply_type.message_text
            options = []

            if start_date and end_date:
                await self.extracted_data_service.update_confirmed_data(
                    conversation_id=conversation_id,
                    field_name=ConfirmedDataFields.DATE_INTERVALS.value,
                    field_value=(start_date, end_date),
                    state=ConversationState.COLLECTING_OBJECTIVE,  # Move to next state
                )
            else:
                await self.extracted_data_service.update_confirmed_data(
                    conversation_id=conversation_id,
                    field_name=ConfirmedDataFields.DATE_INTERVALS.value,
                    field_value=(start_date, end_date),
                    state=ConversationState.COLLECTING_DATES,  # Stay in the same state
                )

                reply_type = SystemReplyType.DATES_ONE_DATE
                confirmation_message = reply_type.message_text
                options = [selected_option]

            return MessageValidator(
                conversation_id=conversation_id,
                role=MessageRole.SYSTEM,
                type=MessageType.TEXT,
                content=confirmation_message,
                selected_option=selected_option,
                options=options,
                system_reply_type=reply_type,
            )

        except Exception as e:
            logger.error('Error handling dates selection: %s', e)
            raise


class KXDashOptionHandler(OptionHandler):
    """Handler for KX Dash task option selections."""

    def __init__(self, kx_dash_service: KXDashService):
        self.kx_dash_service = kx_dash_service

    async def handle(self, selected_option: Option, conversation_id: UUID, **kwargs) -> MessageValidator:
        """Handle KX Dash task selection from options."""
        token = kwargs.get('token', '')
        return await self.kx_dash_service.on_select(selected_option, conversation_id, token=token)


class OptionHandlerFactory:
    """Factory for creating option handlers based on option type."""

    def __init__(
        self,
        extracted_data_service: ExtractedDataService,
        kx_dash_service: KXDashService,
    ):
        self._handlers: Dict[OptionType, OptionHandler] = {
            OptionType.CLIENT_NAME: ClientNameOptionHandler(extracted_data_service),
            OptionType.LDMF_COUNTRY: CountryOptionHandler(extracted_data_service),
            OptionType.DATES: DatesOptionHandler(extracted_data_service),
            OptionType.KX_DASH_TASK: KXDashOptionHandler(kx_dash_service),
        }

    async def handle_option(self, selected_option: Option, conversation_id: UUID, **kwargs) -> MessageValidator:
        """
        Handle the selected option using the appropriate handler.

        Args:
            selected_option: The selected option
            conversation_id: The conversation ID
            **kwargs: Additional arguments to pass to the handler

        Returns:
            MessageValidator: System message confirming the selection

        Raises:
            NotImplementedError: If no handler exists for the option type
        """
        handler = self._handlers.get(selected_option.type)
        if not handler:
            raise NotImplementedError(f'Processing of {selected_option.type} option is not yet implemented')

        return await handler.handle(selected_option, conversation_id, **kwargs)
