"""
System message coordinator for handling system message creation and enrichment.

This coordinator handles the creation of system messages from processing results,
auto-generation of system messages, and enrichment with proactive chat content.
"""

import logging
from typing import List, Sequence, cast
from uuid import UUID

from constants.extracted_data import ConversationState
from constants.message import MessageRole, SuggestedUserPrompt
from schemas import (
    AggregatedData,
    ClientNameOption,
    CombinedMessageSerializer,
    ConfirmedData,
    ConversationMessageProcessingResult,
    DatePickerOption,
    LDMFCountryOption,
    MessageValidator,
    Option,
    SystemMessageSerializer,
)
from services.extracted_data import ExtractedDataService
from services.proactive_chat import ProactiveChatService


__all__ = ['SystemMessageCoordinator']

logger = logging.getLogger(__name__)


class SystemMessageCoordinator:
    """
    Coordinates system message creation and enrichment.
    
    This class handles the creation of system messages from processing results,
    auto-generation of system messages, and enrichment with proactive content.
    """

    def __init__(self, extracted_data_service: ExtractedDataService):
        self.extracted_data_service = extracted_data_service

    async def create_system_message_from_processing_result(
        self,
        message_data: MessageValidator,
        processing_result: ConversationMessageProcessingResult,
        suggested_prompts: Sequence[SuggestedUserPrompt],
    ) -> MessageValidator:
        """
        Create a system message based on the message processing result.
        
        Args:
            message_data: Original message data
            processing_result: Result from message processing
            suggested_prompts: Suggested prompts for the user
            
        Returns:
            MessageValidator: System message data
        """
        options = []
        # Check if we have options from the processing result
        if 'options' in processing_result.data and processing_result.data['options']:
            # Check conversation state to determine option type
            conversation_state = processing_result.data.get('conversation_state')
            if conversation_state:
                options = self._convert_to_option_objects(processing_result.data['options'], conversation_state)

        return MessageValidator(
            conversation_id=message_data.conversation_id,
            role=MessageRole.SYSTEM,
            type=message_data.type,
            content=processing_result.system_reply,
            system_reply_type=processing_result.system_reply_type,
            options=options,
            suggested_prompts=[i.value for i in suggested_prompts],
        )

    async def enrich_system_message_with_proactive_content(
        self,
        system_message: MessageValidator,
        aggregated_data: AggregatedData,
        confirmed_data: ConfirmedData,
        conversation_id: UUID,
        token: str,
    ) -> MessageValidator:
        """
        Enrich system message with proactive chat content.
        
        Args:
            system_message: The system message to enrich
            aggregated_data: Aggregated conversation data
            confirmed_data: Confirmed conversation data
            conversation_id: The conversation ID
            token: Authentication token
            
        Returns:
            MessageValidator: Enriched system message
        """
        proactive_chat_service = ProactiveChatService(
            aggregated_data=aggregated_data, confirmed_data=confirmed_data
        )
        
        # Get proactive message content
        enriched_message_content = proactive_chat_service.get_enriched_system_message()
        system_message.content = f'{system_message.content}\n\n{enriched_message_content}'

        # Get proactive options
        if enriched_message_content and not system_message.options:
            missing_data_response = await self.extracted_data_service.get_missing_required_data_prompts(
                conversation_id=conversation_id,
                token=token,
                confirmed_data=confirmed_data,
            )
            proactive_options = self._convert_to_option_objects(
                missing_data_response.options, missing_data_response.conversation_state
            )
            system_message.options = proactive_options

        return system_message

    async def create_dash_discard_response(
        self,
        user_message,
        processing_result: ConversationMessageProcessingResult,
        suggested_prompts: Sequence[SuggestedUserPrompt],
        create_message_func,
    ):
        """
        Create a response for dash discard messages.
        
        Args:
            user_message: The user message
            processing_result: Processing result
            suggested_prompts: Suggested prompts
            create_message_func: Function to create messages
            
        Returns:
            CombinedMessageSerializer: The response
        """

        
        message = MessageValidator(
            conversation_id=user_message.conversation_id,
            role=MessageRole.SYSTEM,
            type=user_message.type,
            content=processing_result.system_reply,
            suggested_prompts=[i.value for i in suggested_prompts],
            system_reply_type=processing_result.system_reply_type,
        )

        response = CombinedMessageSerializer(
            user=user_message,
            system=cast(SystemMessageSerializer, await create_message_func(message)),
        )

        return response

    def _convert_to_option_objects(
        self, raw_options: List | None, conversation_state: ConversationState
    ) -> List[Option]:
        """Convert raw options to proper option objects based on conversation state."""
        if not raw_options or not conversation_state:
            return []

        if conversation_state == ConversationState.COLLECTING_CLIENT_NAME:
            return [ClientNameOption(client_name=name) for name in raw_options]
        elif conversation_state == ConversationState.COLLECTING_COUNTRY:
            return [LDMFCountryOption(ldmf_country=name) for name in raw_options]
        elif conversation_state == ConversationState.COLLECTING_DATES:
            return [DatePickerOption(start_date=start_date, end_date=end_date) for start_date, end_date in raw_options]
        return []
