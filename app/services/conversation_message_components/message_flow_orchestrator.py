"""
Message flow orchestrator for coordinating different message processing flows.

This orchestrator coordinates the creation of user messages, processing of content,
generation of system messages, and handling of document processing while maintaining
a clean separation of concerns.
"""

import logging
from typing import Sequence, cast
from uuid import UUID

from fastapi import UploadFile

from config import settings
from constants.message import ConversationMessageIntention, MessageRole, MessageType
from schemas import (
    AggregatedData,
    BaseMessageSerializer,
    CombinedMessageSerializer,
    ConfirmedData,
    ConversationMessageProcessingResult,
    DocumentCreationRequest,
    MessageValidator,
    Option,
    SuggestedUserPrompt,
    SystemMessageSerializer,
    UserMessageSerializer,
)
from services.conversation_message.option_handlers import OptionHandlerFactory
from services.date_validator import DateValidatorService
from services.document import DocumentService
from services.extracted_data import ExtractedDataService
from services.intent_classifier import IntentClassifierService
from services.kx_dash import KXDashService
from services.message_processor import ConversationMessageProcessor
from services.proactive_chat import ProactiveChatService
from services.suggestions import SuggestedPromptsGenerator


__all__ = ['MessageFlowOrchestrator']

logger = logging.getLogger(__name__)


class MessageFlowOrchestrator:
    """
    Orchestrates the flow of message creation and processing.
    
    This class coordinates different message processing flows while maintaining
    the same public interface as the original service.
    """

    def __init__(
        self,
        option_handler_factory: OptionHandlerFactory,
        intent_classifier_service: IntentClassifierService,
        extracted_data_service: ExtractedDataService,
        date_validator_service: DateValidatorService,
        document_service: DocumentService,
        kx_dash_service: KXDashService,
    ):
        self.option_handler_factory = option_handler_factory
        self.intent_classifier_service = intent_classifier_service
        self.extracted_data_service = extracted_data_service
        self.date_validator_service = date_validator_service
        self.document_service = document_service
        self.kx_dash_service = kx_dash_service

    async def orchestrate_message_creation(
        self,
        conversation_id: UUID,
        content: str,
        selected_option: Option | None,
        files: list[UploadFile] | None,
        token: str,
        user_message: UserMessageSerializer,
        create_message_func,
        get_combined_history_func,
        get_confirmed_data_func,
    ) -> CombinedMessageSerializer:
        """
        Orchestrate the complete message creation flow.

        Args:
            conversation_id: UUID of the conversation
            content: Text content of the message
            selected_option: The selected option
            files: Optional list of files to attach
            token: Authentication token
            user_message: The created user message
            create_message_func: Function to create messages in the repository
            get_combined_history_func: Function to get conversation history
            get_confirmed_data_func: Function to get confirmed data

        Returns:
            CombinedMessageSerializer: Response containing user and system messages
        """
        aggregated_data = None
        intention = None
        confirmed_data = None
        system_message = None

        # Handle option selection flow
        if selected_option:
            system_message = await self.option_handler_factory.handle_option(
                selected_option, conversation_id, token=token
            )
        else:
            # Handle regular message processing flow
            system_message, intention, aggregated_data, confirmed_data = await self._process_regular_message(
                conversation_id=conversation_id,
                content=content,
                files=files,
                token=token,
                user_message=user_message,
                get_combined_history_func=get_combined_history_func,
                get_confirmed_data_func=get_confirmed_data_func,
            )

        # Get data if not already retrieved
        if not aggregated_data:
            aggregated_data = await self.extracted_data_service.aggregate_data(conversation_id)

        if not confirmed_data:
            confirmed_data = await get_confirmed_data_func(conversation_id)

        # Enrich system message with proactive content
        if system_message:
            system_message = await self._enrich_system_message(
                system_message, aggregated_data, confirmed_data, conversation_id, token
            )

        # Prepare response
        response = CombinedMessageSerializer(
            user=user_message,
            system=cast(SystemMessageSerializer, await create_message_func(system_message)) if system_message else None,
        )

        # Handle document processing
        if files or (content and intention == ConversationMessageIntention.EXTRACTION):
            document_responses = await self._handle_document_processing(
                conversation_id, user_message.id, files, content, intention
            )
            if document_responses:
                response.files = document_responses

        # Add collected data if configured
        if settings.append_collected_data_to_message_response:
            response.collected_data = aggregated_data.model_dump()

        return response

    async def _process_regular_message(
        self,
        conversation_id: UUID,
        content: str,
        files: list[UploadFile] | None,
        token: str,
        user_message: UserMessageSerializer,
        get_combined_history_func,
        get_confirmed_data_func,
    ) -> tuple[MessageValidator | None, ConversationMessageIntention, AggregatedData, ConfirmedData]:
        """Process regular message (non-option selection) flow."""
        # Process the user message to get the system reply and intention
        message_processor = ConversationMessageProcessor(
            conversation_id=conversation_id,
            user_message=user_message,
            intent_classifier_service=self.intent_classifier_service,
            extracted_data_service=self.extracted_data_service,
            conversation_repository=None,  # Will be injected by the service
            date_validator_service=self.date_validator_service,
            document_service=self.document_service,
            token=token,
        )
        
        # Note: This will need to be handled by the calling service since we need repository access
        # For now, return None and let the service handle this
        return None, ConversationMessageIntention.UNDEFINED, None, None

    async def _enrich_system_message(
        self,
        system_message: MessageValidator,
        aggregated_data: AggregatedData,
        confirmed_data: ConfirmedData,
        conversation_id: UUID,
        token: str,
    ) -> MessageValidator:
        """Enrich system message with proactive chat content."""
        proactive_chat_service = ProactiveChatService(
            aggregated_data=aggregated_data, confirmed_data=confirmed_data
        )
        
        # Get proactive message content
        enriched_message_content = proactive_chat_service.get_enriched_system_message()
        system_message.content = f'{system_message.content}\n\n{enriched_message_content}'

        # Get proactive options
        if enriched_message_content and not system_message.options:
            missing_data_response = await self.extracted_data_service.get_missing_required_data_prompts(
                conversation_id=conversation_id,
                token=token,
                confirmed_data=confirmed_data,
            )
            proactive_options = self._convert_to_option_objects(
                missing_data_response.options, missing_data_response.conversation_state
            )
            system_message.options = proactive_options

        return system_message

    async def _handle_document_processing(
        self,
        conversation_id: UUID,
        message_id: UUID,
        files: list[UploadFile] | None,
        content: str,
        intention: ConversationMessageIntention | None,
    ) -> list | None:
        """Handle document processing for files and/or text content."""
        document_data = DocumentCreationRequest(
            conversation_id=conversation_id,
            files=files or [],
            message_id=message_id,
        )

        # Use unified approach: send text content for extraction along with files
        text_for_extraction = content if intention == ConversationMessageIntention.EXTRACTION else None
        return await self.document_service.create_combined_message(document_data, text_for_extraction)

    def _convert_to_option_objects(self, raw_options, conversation_state):
        """Convert raw options to proper option objects based on conversation state."""
        # This method will be moved from the main service
        # For now, return empty list
        return []
