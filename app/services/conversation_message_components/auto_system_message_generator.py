"""
Auto system message generator for generating system messages when the last message is from a user.

This generator handles the logic for automatically creating system messages based on
conversation state and extracted data when retrieving the last message.
"""

import logging
from typing import cast
from uuid import UUID

from constants.extracted_data import ConversationState
from constants.message import ALL_REQUIRED_FIELDS_EXTRACTED_DOCS, MessageRole, SystemReplyType
from exceptions import EntityNotFoundError
from schemas import (
    BaseMessageSerializer,
    MessageValidator,
    UserMessageSerializer,
)
from services.extracted_data import ExtractedDataService
from services.system_message_generation import SystemMessageGenerationService

from .conversation_state_coordinator import ConversationStateCoordinator


__all__ = ['AutoSystemMessageGenerator']

logger = logging.getLogger(__name__)


class AutoSystemMessageGenerator:
    """
    Generates system messages automatically when the last message is from a user.
    
    This generator handles the complex logic for determining what type of system
    message to generate based on conversation state and extracted data.
    """

    def __init__(
        self,
        extracted_data_service: ExtractedDataService,
        system_message_generation_service: SystemMessageGenerationService,
        conversation_state_coordinator: ConversationStateCoordinator,
    ):
        self.extracted_data_service = extracted_data_service
        self.system_message_service = system_message_generation_service
        self.conversation_state_coordinator = conversation_state_coordinator

    async def generate_system_message_for_last_user_message(
        self,
        conversation_id: UUID,
        last_message: BaseMessageSerializer,
        token: str,
        get_combined_history_func,
        get_confirmed_data_func,
        get_conversation_func,
        get_filenames_for_message_func,
        get_suggested_prompts_func,
        update_confirmed_data_and_state_func,
    ) -> MessageValidator:
        """
        Generate a system message for the last user message.

        Args:
            conversation_id: The conversation ID
            last_message: The last message (should be from user)
            token: Authentication token
            get_combined_history_func: Function to get conversation history
            get_confirmed_data_func: Function to get confirmed data
            get_conversation_func: Function to get conversation
            get_filenames_for_message_func: Function to get filenames for message
            get_suggested_prompts_func: Function to get suggested prompts
            update_confirmed_data_and_state_func: Function to update confirmed data and state

        Returns:
            MessageValidator: The generated system message
        """
        try:
            conversation_message_history = await get_combined_history_func(conversation_id)

            # Get aggregated data for the conversation
            aggregated_data = await self.extracted_data_service.aggregate_data(conversation_id)

            # Handle confirmed data logic
            await self.conversation_state_coordinator.handle_confirmed_data_update(
                conversation_id, aggregated_data, token,
                get_confirmed_data_func,
                update_confirmed_data_and_state_func,
            )

            confirmed_data = await get_confirmed_data_func(conversation_id)
            conversation = await get_conversation_func(conversation_id)
            if not conversation:
                raise EntityNotFoundError('Conversation', str(conversation_id))

            # Check if we should use client name single confirmation message
            should_use_client_confirmation = (
                conversation
                and str(conversation.State) == str(ConversationState.COLLECTING_CLIENT_NAME)
                and aggregated_data.is_complete
                and not confirmed_data.client_name
            )

            if should_use_client_confirmation:
                reply_type = SystemReplyType.CLIENT_NAME_SINGLE_CONFIRMATION
                # Get filename from the previous user message
                filenames = await get_filenames_for_message_func(last_message.id)
                filename = filenames[0] if filenames else None  # no files uploaded

                if filename:
                    filename = filename.split('.')[0]
                    formatted_content = ALL_REQUIRED_FIELDS_EXTRACTED_DOCS.format(
                        client_name=aggregated_data.client_name[0], filename=filename
                    )
                else:
                    formatted_content = reply_type.message_text.format(client_name=aggregated_data.client_name[0])

                extracted_options = []

            else:
                # Get confirmed data to avoid showing options for already confirmed fields
                confirmed_data = await get_confirmed_data_func(conversation_id)

                # Format the extracted data message
                formatted_content, reply_type = self.system_message_service.generate_system_message(
                    aggregated_data, confirmed_data
                )
                extracted_options = self.system_message_service.generate_options(
                    aggregated_data, confirmed_data, ConversationState(conversation.State)
                )

            suggested_prompts = await get_suggested_prompts_func(
                conversation_id=conversation_id,
                user_message=cast(UserMessageSerializer, last_message),
                files=None,
                token=token,
                aggregated_data=aggregated_data,
                confirmed_data=confirmed_data,
                intention=None,  # Will be set by the calling function
                conversation_message_history=conversation_message_history,
                current_reply_type=reply_type,
            )

            # Create system message
            system_message_data = MessageValidator(
                conversation_id=conversation_id,
                role=MessageRole.SYSTEM,
                type=last_message.type,
                content=formatted_content,
                system_reply_type=reply_type,
                options=extracted_options,
                suggested_prompts=[str(i) for i in suggested_prompts],
            )

            return system_message_data

        except Exception as e:
            logger.warning('Failed to generate system message for conversation %s: %s', conversation_id, e)
            raise
