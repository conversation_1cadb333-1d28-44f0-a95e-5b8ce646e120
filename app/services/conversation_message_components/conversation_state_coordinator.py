"""
Conversation state coordinator for managing conversation state and confirmed data updates.

This coordinator handles the logic for updating confirmed data based on aggregated data,
managing conversation state transitions, and determining when data should be updated.
"""

from datetime import date
import logging
from uuid import UUID

from constants.extracted_data import ConversationState
from schemas import AggregatedData, ConfirmedData
from services.extracted_data import ExtractedDataService


__all__ = ['ConversationStateCoordinator']

logger = logging.getLogger(__name__)


class ConversationStateCoordinator:
    """
    Coordinates conversation state management and confirmed data updates.
    
    This class handles the complex logic for updating confirmed data based on
    aggregated data state and managing conversation state transitions.
    """

    def __init__(self, extracted_data_service: ExtractedDataService):
        self.extracted_data_service = extracted_data_service

    async def handle_confirmed_data_update(
        self, 
        conversation_id: UUID, 
        aggregated_data: AggregatedData, 
        token: str,
        get_confirmed_data_func,
        update_confirmed_data_and_state_func,
    ) -> None:
        """
        Handle confirmed data logic based on aggregated data state.

        Args:
            conversation_id: The conversation ID
            aggregated_data: The aggregated data from all sources
            token: Authentication token
            get_confirmed_data_func: Function to get confirmed data
            update_confirmed_data_and_state_func: Function to update confirmed data and state
        """
        try:
            # Fetch confirmed data from existing conversation
            confirmed_data = await get_confirmed_data_func(conversation_id)

            # Check if we need to update confirmed data
            should_update = False

            # If confirmed data is empty and aggregated has all required fields with single values
            if confirmed_data.is_empty and aggregated_data.is_complete:
                logger.info('Confirmed data is empty and aggregated data is complete for conversation %s', conversation_id)
                # Apply all fields from aggregated except client_name
                should_update, confirmed_data = await self._update_fields_from_aggregated_data(
                    aggregated_data, confirmed_data, token
                )

            elif confirmed_data.is_empty and not aggregated_data.is_complete:
                logger.info(
                    'Confirmed data is empty and aggregated data is not complete for conversation %s', conversation_id
                )
                should_update, confirmed_data = await self._update_fields_from_aggregated_data(
                    aggregated_data, confirmed_data, token
                )

            # If confirmed data has some fields - update from aggregated
            elif not confirmed_data.is_empty and not confirmed_data.required_fields_are_complete:
                logger.info('Confirmed data has some fields, updating from aggregated for conversation %s', conversation_id)

                # Update ldmf_country if not confirmed and available in aggregated
                if (
                    not confirmed_data.ldmf_country
                    and aggregated_data.ldmf_country
                    and len(aggregated_data.ldmf_country) == 1
                ):
                    verified_countries = await self.extracted_data_service.ldmf_country_service.verify_ldmf_country(
                        aggregated_data.ldmf_country[0], token
                    )
                    if verified_countries and len(verified_countries) == 1:
                        confirmed_data.ldmf_country = verified_countries[0]
                        should_update = True

                # Update date_intervals if not confirmed and available in aggregated
                if (
                    not confirmed_data.date_intervals
                    and aggregated_data.date_intervals
                    and len(aggregated_data.date_intervals) == 1
                ):
                    start_date, end_date = aggregated_data.date_intervals[0]
                    if self._is_date_unambiguous_and_complete(start_date, end_date):
                        confirmed_data.date_intervals = (start_date, end_date)
                        should_update = True

                # Update objective_and_scope if not confirmed and available in aggregated
                if not confirmed_data.objective_and_scope and aggregated_data.objective_and_scope:
                    confirmed_data.objective_and_scope = aggregated_data.objective_and_scope
                    should_update = True

                # Update outcomes if not confirmed and available in aggregated
                if not confirmed_data.outcomes and aggregated_data.outcomes:
                    confirmed_data.outcomes = aggregated_data.outcomes
                    should_update = True

            # If confirmed data has all fields - do nothing
            elif confirmed_data.required_fields_are_complete:
                logger.info('Confirmed data is complete, no update needed for conversation %s', conversation_id)
                return

            # Save to database and set state to collecting client name if confirmed data was updated
            if should_update:
                logger.info(
                    'Updating confirmed data and setting state to COLLECTING_CLIENT_NAME for conversation %s', conversation_id
                )
                await update_confirmed_data_and_state_func(
                    public_id=conversation_id,
                    confirmed_data=confirmed_data,
                    state=ConversationState.COLLECTING_CLIENT_NAME,
                )

        except Exception:  # pragma: no cover
            logger.exception('Error handling confirmed data update for conversation %s', conversation_id)
            raise

    async def _update_fields_from_aggregated_data(
        self, aggregated_data: AggregatedData, confirmed_data: ConfirmedData, token: str
    ) -> tuple[bool, ConfirmedData]:
        """Update confirmed data fields from aggregated data."""
        should_update = False
        
        if aggregated_data.ldmf_country and len(aggregated_data.ldmf_country) == 1:
            verified_countries = await self.extracted_data_service.ldmf_country_service.verify_ldmf_country(
                aggregated_data.ldmf_country[0], token
            )
            if verified_countries and len(verified_countries) == 1:
                confirmed_data.ldmf_country = verified_countries[0]
                should_update = True

        if aggregated_data.date_intervals and len(aggregated_data.date_intervals) == 1:
            start_date, end_date = aggregated_data.date_intervals[0]
            if self._is_date_unambiguous_and_complete(start_date, end_date):
                confirmed_data.date_intervals = (start_date, end_date)
                should_update = True

        if aggregated_data.objective_and_scope:
            confirmed_data.objective_and_scope = aggregated_data.objective_and_scope
            should_update = True

        if aggregated_data.outcomes:
            confirmed_data.outcomes = aggregated_data.outcomes
            should_update = True

        return should_update, confirmed_data

    @staticmethod
    def _is_date_unambiguous_and_complete(start_date: str | None, end_date: str | None) -> bool:
        """Check if the date is unambiguous and complete."""
        if not start_date or not end_date:
            return False

        processed_start_date = date.fromisoformat(start_date)
        processed_end_date = date.fromisoformat(end_date)

        return processed_start_date.day > 12 and processed_end_date.day > 12
