import logging
from typing import Sequence, cast
from uuid import UUID

from fastapi import UploadFile

from config import settings
from constants.message import (
    ConversationMessageIntention,
    MessageRole,
    MessageType,
    SuggestedUserPrompt,
    SystemReplyType,
)
from exceptions import EntityNotFoundError
from repositories import ConversationMessageRepository, ConversationRepository, DocumentDbRepository
from schemas import (
    AggregatedData,
    BaseMessageSerializer,
    CombinedMessageSerializer,
    ConfirmedData,
    ConversationMessageProcessingResult,
    MessageValidator,
    Option,
    SystemMessageSerializer,
    UserMessageSerializer,
)
from services.conversation_message_components import (
    AutoSystemMessageGenerator,
    ConversationStateCoordinator,
    DocumentProcessingCoordinator,
    OptionHandlerFactory,
    SystemMessageCoordinator,
)
from services.date_validator import DateValidatorService
from services.document import DocumentService
from services.extracted_data import ExtractedDataService
from services.intent_classifier import IntentClassifierService
from services.kx_dash import KXDashService
from services.message_processor import ConversationMessageProcessor
from services.suggestions import SuggestedPromptsGenerator
from services.system_message_generation import SystemMessageGenerationService


__all__ = ['ConversationMessageService']

logger = logging.getLogger(__name__)


class ConversationMessageService:
    """Service for conversation message-related business logic."""

    def __init__(
        self,
        conversation_message_repository: ConversationMessageRepository,
        conversation_repository: ConversationRepository,
        document_service: DocumentService,
        document_db_repository: DocumentDbRepository,
        kx_dash_service: KXDashService,
        intent_classifier_service: IntentClassifierService,
        extracted_data_service: ExtractedDataService,
        date_validator_service: DateValidatorService,
        system_message_generation_service: SystemMessageGenerationService,
    ):
        self.conversation_message_repository = conversation_message_repository
        self.conversation_repository = conversation_repository
        self.document_service = document_service
        self.document_db_repository = document_db_repository
        self.kx_dash_service = kx_dash_service
        self.intent_classifier_service = intent_classifier_service
        self.extracted_data_service = extracted_data_service
        self.date_validator_service = date_validator_service
        self.system_message_service = system_message_generation_service

        # Initialize coordinators and factories
        self.option_handler_factory = OptionHandlerFactory(
            extracted_data_service=extracted_data_service,
            kx_dash_service=kx_dash_service,
        )
        self.system_message_coordinator = SystemMessageCoordinator(
            extracted_data_service=extracted_data_service,
        )
        self.conversation_state_coordinator = ConversationStateCoordinator(
            extracted_data_service=extracted_data_service,
        )
        self.document_processing_coordinator = DocumentProcessingCoordinator(
            document_service=document_service,
        )
        self.auto_system_message_generator = AutoSystemMessageGenerator(
            extracted_data_service=extracted_data_service,
            system_message_generation_service=system_message_generation_service,
            conversation_state_coordinator=self.conversation_state_coordinator,
        )

    async def create(
        self,
        conversation_id: UUID,
        content: str,
        selected_option: Option | None,
        files: list[UploadFile] | None,
        token: str,
    ) -> CombinedMessageSerializer:
        """
        Create a new conversation message with attached files.

        Args:
            conversation_id: UUID of the conversation
            content: Text content of the message
            selected_option: The selected option
            files: Optional list of files to attach

        Returns:
            Response containing both user message and system message with expected entity

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
            MaximumDocumentsNumberExceeded: If too many documents are attached
            MaximumDocumentsSizeExceeded: If documents exceed size limit
            ValueError: If file validation fails
        """

        content = content.strip()
        if files:
            message_type = MessageType.TEXT_WITH_FILE if content else MessageType.FILE
        else:
            message_type = MessageType.TEXT

        user_message_to_persist = MessageValidator(
            conversation_id=conversation_id,
            role=MessageRole.USER,
            type=message_type,
            content=content,
            selected_option=selected_option,
            files=files,
            system_reply_type=None,
        )

        user_message = await self.create_message(user_message_to_persist)
        user_message = cast(UserMessageSerializer, user_message)

        aggregated_data = None
        intention = None
        confirmed_data = None

        if selected_option:
            system_message = await self.option_handler_factory.handle_option(
                selected_option, conversation_id, token=token
            )
        else:
            # Process the user message to get the system reply and intention
            message_processor = ConversationMessageProcessor(
                conversation_id=conversation_id,
                user_message=user_message,
                intent_classifier_service=self.intent_classifier_service,
                extracted_data_service=self.extracted_data_service,
                conversation_repository=self.conversation_repository,
                date_validator_service=self.date_validator_service,
                document_service=self.document_service,
                token=token,
            )
            message_processing_result: ConversationMessageProcessingResult = await message_processor.run()
            intention = message_processing_result.intention
            user_message_to_persist.intention = intention

            # Get aggregated extracted and confirmed data after message processing.
            aggregated_data = await self.extracted_data_service.aggregate_data(conversation_id)
            confirmed_data = await self.conversation_repository.get_confirmed_data(conversation_id)

            # Get conversation message history for suggested prompts tracking.
            conversation_message_history = await self.conversation_message_repository.get_combined_history(
                conversation_id
            )

            suggested_prompts = await self.get_suggested_prompts(
                conversation_message_history=conversation_message_history,
                conversation_id=conversation_id,
                user_message=user_message,
                files=files,
                token=token,
                aggregated_data=aggregated_data,
                confirmed_data=confirmed_data,
                intention=intention,
            )

            # Update the user message with the determined intention
            await self.update_message_fields(user_message.id, {'Intention': intention})

            if intention == ConversationMessageIntention.DASH_DISCARD:
                dash_discard_message = await self.system_message_coordinator.create_dash_discard_response(
                    user_message, message_processing_result, suggested_prompts, self.create_message
                )
                return dash_discard_message

            system_message = None
            should_generate_system_message = not (
                (content and intention == ConversationMessageIntention.EXTRACTION) or files
            )
            if should_generate_system_message:
                system_message = await self.system_message_coordinator.create_system_message_from_processing_result(
                    user_message_to_persist, message_processing_result, suggested_prompts
                )
                if not system_message.content:
                    system_message = None

        if not aggregated_data:
            aggregated_data = await self.extracted_data_service.aggregate_data(conversation_id)

        if not confirmed_data:
            confirmed_data = await self.conversation_repository.get_confirmed_data(conversation_id)

        # Enrich system message with proactive content
        if system_message:
            system_message = await self.system_message_coordinator.enrich_system_message_with_proactive_content(
                system_message, aggregated_data, confirmed_data, conversation_id, token
            )

        # Prepare & return response
        response = CombinedMessageSerializer(
            user=user_message,
            system=cast(SystemMessageSerializer, await self.create_message(system_message)) if system_message else None,
        )

        # Handle document processing
        document_responses = await self.document_processing_coordinator.trigger_document_processing(
            conversation_id=user_message.conversation_id,
            message_id=response.user.id,
            files=files,
            content=content,
            intention=intention,
        )
        if document_responses:
            response.files = document_responses

        if settings.append_collected_data_to_message_response:
            response.collected_data = aggregated_data.model_dump()

        return response

    async def get_suggested_prompts(
        self,
        conversation_id: UUID,
        user_message: UserMessageSerializer,
        files: list[UploadFile] | None,
        token: str,
        aggregated_data: AggregatedData,
        confirmed_data: ConfirmedData,
        intention: ConversationMessageIntention,
        conversation_message_history: list[CombinedMessageSerializer],
        current_reply_type: SystemReplyType | None = None,
    ) -> list[SuggestedUserPrompt]:
        dash_task_activity_id: int | None = await self.conversation_repository.get_dash_task_activity_id(
            conversation_id
        )

        # Generate suggested replies for the user
        suggested_prompts_generator = SuggestedPromptsGenerator(
            conversation_id=conversation_id,
            user_message=user_message,
            conversation_message_history=conversation_message_history,
            intention=intention,
            aggregated_data=aggregated_data,
            confirmed_data=confirmed_data,
            files=files,
            dash_task_activity_id=dash_task_activity_id,
            current_reply_type=current_reply_type,
        )
        return await suggested_prompts_generator.run()

    async def create_message(self, message_data: MessageValidator) -> BaseMessageSerializer:
        """
        Create a new conversation message.

        Args:
            message_data: Data for creating the message

        Returns:
            Response with the created message data

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
            DatabaseException: If there's an error creating the message
        """
        logger.debug('Creating new message for conversation ID: %s', message_data.conversation_id)
        try:
            return await self.conversation_message_repository.create(message_data)

        except Exception as e:  # pragma: no cover
            logger.error('Error creating message: %s', e)
            raise

    async def get(self, public_id: UUID) -> BaseMessageSerializer:
        """
        Get a message by its ID.

        Args:
            public_id: The ID of the message

        Returns:
            The message response

        Raises:
            EntityNotFoundError: If the message doesn't exist
            DatabaseException: If there's an error retrieving the message
        """
        try:
            logger.debug('Retrieving message with ID: %s', public_id)
            return await self.conversation_message_repository.get(public_id)

        except Exception as e:  # pragma: no cover
            logger.error('Error retrieving message: %s', e)
            raise

    async def list(self, public_id: UUID) -> Sequence[BaseMessageSerializer]:
        """
        Get all messages for a specific conversation.

        Args:
            public_id: The ID of the conversation

        Returns:
            Sequence of messages for the conversation

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
            DatabaseException: If there's an error retrieving the messages
        """
        if not await self.conversation_repository.exists(public_id):
            raise EntityNotFoundError('Conversation', str(public_id))

        try:
            logger.debug('Retrieving all messages for conversation with ID: %s', public_id)
            return await self.conversation_message_repository.list(public_id)

        except Exception as e:  # pragma: no cover
            logger.error('Error retrieving messages for conversation %s: %s', public_id, e)
            raise

    async def get_last(self, public_id: UUID, token: str) -> BaseMessageSerializer:
        """
        Get the last message for a specific conversation.

        Args:
            public_id: The ID of the conversation
            token: The authentication token

        Returns:
            The last message for the conversation

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
            DatabaseException: If there's an error retrieving the last message
        """
        try:
            logger.debug('Retrieving last message for conversation with ID: %s', public_id)

            last_message = await self.conversation_message_repository.get_last(public_id)
            # Check if the last message is from a user and we should generate a system response

            if last_message.role != MessageRole.USER:
                return last_message

            logger.debug('Last message is from user, generating system response for conversation %s', public_id)

            try:
                # Generate system message using the auto generator
                system_message_data = await self.auto_system_message_generator.generate_system_message_for_last_user_message(
                    conversation_id=public_id,
                    last_message=last_message,
                    token=token,
                    get_combined_history_func=self.conversation_message_repository.get_combined_history,
                    get_confirmed_data_func=self.conversation_repository.get_confirmed_data,
                    get_conversation_func=self.conversation_repository.get,
                    get_filenames_for_message_func=self.document_db_repository.get_filenames_for_message,
                    get_suggested_prompts_func=self._get_suggested_prompts_for_auto_generation,
                    update_confirmed_data_and_state_func=self.conversation_repository.update_confirmed_data_and_state,
                )

                # Create and return the system message
                system_message = await self.create_message(system_message_data)
                logger.info('Generated system message %s for conversation %s', system_message.id, public_id)
                return system_message

            except Exception as e:
                logger.warning('Failed to generate system message for conversation %s: %s', public_id, e)
                raise

        except Exception as e:  # pragma: no cover
            logger.error('Error retrieving last message for conversation %s: %s', public_id, e)
            raise

    async def _get_suggested_prompts_for_auto_generation(
        self,
        conversation_id,
        user_message,
        files,
        token,
        aggregated_data,
        confirmed_data,
        intention,
        conversation_message_history,
        current_reply_type=None,
    ):
        """Helper method for getting suggested prompts during auto system message generation."""
        return await self.get_suggested_prompts(
            conversation_id=conversation_id,
            user_message=user_message,
            files=files,
            token=token,
            aggregated_data=aggregated_data,
            confirmed_data=confirmed_data,
            intention=ConversationMessageIntention.EXTRACTION,  # Default for auto-generation
            conversation_message_history=conversation_message_history,
            current_reply_type=current_reply_type,
        )

    async def delete_many(self, conversation_id: UUID) -> None:
        """
        Delete all messages for a specific conversation.

        Args:
            conversation_id: The ID of the conversation

        Returns:
            None

        Raises:
            DatabaseException: If there's an error deleting the messages
        """
        try:
            logger.debug('Deleting all messages for conversation with ID: %s', conversation_id)
            await self.conversation_message_repository.delete_many(conversation_id)

        except Exception as e:  # pragma: no cover
            logger.error('Error deleting messages for conversation %s: %s', conversation_id, e)
            raise

    async def update_message_fields(self, message_id: UUID, fields_to_update: dict) -> None:
        """
        Update specific fields of a conversation message.

        Args:
            message_id: The ID of the message to update.
            fields_to_update: A dictionary where keys are field names and values are the new values.
        """
        try:
            logger.debug('Updating message %s with fields: %s', message_id, fields_to_update)
            await self.conversation_message_repository.update_fields(message_id, fields_to_update)
        except Exception as e:
            logger.error('Error updating message %s: %s', message_id, e)
            raise

    async def get_owner_id(self, message_id: UUID) -> UUID | None:
        """
        Get an owner ID for the message.

        Args:
            message_id: The ID of the message

        Returns:
            The user ID if the message exists, None otherwise
        """
        try:
            logger.debug('Retrieving an owner ID for the message: %s', message_id)
            return await self.conversation_message_repository.get_owner_id(message_id)

        except Exception:  # pragma: no cover
            logger.exception('Failed to retrieve message owner ID')
            raise
